<!DOCTYPE html>
<html>
<head>
    <style>
        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            display: block;
        }
        #canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        #message {
            font-family: Consolas;
            font-size: 1.2em;
            color: #ccc;
            background-color: black;
            font-weight: bold;
            z-index: 2;
            position: absolute;
            padding: 10px;
        }
    </style>
</head>
<body>
    <div id="message">Loading...</div>
    <canvas id="canvas"></canvas>
</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/110/three.min.js"></script>

<script id='test-shader' type='x-shader/x-fragment'>
precision highp float;
uniform vec2 resolution;
uniform float time;

struct Ray {
    vec3 origin;
    vec3 direction;
};

struct Sphere {
    vec3 position;
    float radius;
    vec3 color;
    float reflection;
    float refraction;
    float ior;
};

struct HitInfo {
    bool hit;
    float t;
    vec3 position;
    vec3 normal;
    vec3 color;
    float reflection;
    float refraction;
    float ior;
    bool enteringPrimitive;
};

HitInfo getEmptyHit() {
    return HitInfo(false, 0.0, vec3(0.0), vec3(0.0), vec3(0.0), 0.0, 0.0, 0.0, false);
}

HitInfo intersectSphere(Ray ray, Sphere sphere) {
    vec3 oc = ray.origin - sphere.position;
    float a = dot(ray.direction, ray.direction);
    float b = 2.0 * dot(oc, ray.direction);
    float c = dot(oc, oc) - sphere.radius * sphere.radius;
    float discriminant = b * b - 4.0 * a * c;
    
    if (discriminant < 0.0) return getEmptyHit();
    
    float t1 = (-b - sqrt(discriminant)) / (2.0 * a);
    float t2 = (-b + sqrt(discriminant)) / (2.0 * a);
    
    float t = t1 > 0.001 ? t1 : (t2 > 0.001 ? t2 : -1.0);
    if (t < 0.0) return getEmptyHit();
    
    vec3 hitPos = ray.origin + t * ray.direction;
    vec3 normal = normalize(hitPos - sphere.position);
    
    // Check if we're inside the sphere
    bool entering = length(ray.origin - sphere.position) > sphere.radius;
    if (!entering) normal = -normal;
    
    return HitInfo(true, t, hitPos, normal, sphere.color, sphere.reflection, sphere.refraction, sphere.ior, entering);
}

vec3 background(Ray ray) {
    return vec3(0.2) + vec3(0.8, 0.6, 0.5) * max(0.0, ray.direction.y);
}

Ray getFragCoordRay(vec2 fragCoord) {
    vec2 uv = (fragCoord - 0.5 * resolution.xy) / resolution.y;
    vec3 origin = vec3(0, 0, 1);
    vec3 direction = normalize(vec3(uv, -1.0));
    return Ray(origin, direction);
}

vec3 trace(Ray ray) {
    vec3 color = vec3(0.0);
    vec3 mask = vec3(1.0);
    
    for (int bounce = 0; bounce < 3; bounce++) {
        // Create glass sphere
        Sphere glassSphere;
        glassSphere.position = vec3(0, 0, -3);
        glassSphere.radius = 1.0;
        glassSphere.color = vec3(0.0);
        glassSphere.reflection = 0.1;
        glassSphere.refraction = 0.9;
        glassSphere.ior = 1.5;
        
        HitInfo hit = intersectSphere(ray, glassSphere);
        
        if (!hit.hit) {
            color += mask * background(ray);
            break;
        }
        
        // Add some basic lighting
        vec3 lightDir = normalize(vec3(1, 1, 1));
        float lighting = max(0.0, dot(hit.normal, lightDir));
        color += mask * hit.color * lighting * 0.1;
        
        // Handle refraction
        if (hit.refraction > 0.0) {
            vec3 normal = hit.normal;
            float eta = hit.enteringPrimitive ? 1.0 / hit.ior : hit.ior;
            
            if (!hit.enteringPrimitive) {
                normal = -normal;
            }
            
            vec3 refractDir = refract(ray.direction, normal, eta);
            
            if (length(refractDir) > 0.0) {
                ray.origin = hit.position - 0.001 * hit.normal;
                ray.direction = refractDir;
                mask *= hit.refraction;
                continue;
            }
        }
        
        // Handle reflection
        if (hit.reflection > 0.0) {
            ray.origin = hit.position + 0.001 * hit.normal;
            ray.direction = reflect(ray.direction, hit.normal);
            mask *= hit.reflection;
            continue;
        }
        
        break;
    }
    
    return color;
}

void main() {
    Ray ray = getFragCoordRay(gl_FragCoord.xy);
    vec3 color = trace(ray);
    
    // Tone mapping
    color = pow(color, vec3(1.0/2.2));
    
    gl_FragColor = vec4(color, 1.0);
}
</script>

<script type="text/javascript">
    console.log("Starting raytracer...");
    
    let canvas = document.getElementById('canvas');
    let message = document.getElementById('message');
    
    let gl = canvas.getContext('webgl2');
    if (gl == null) {
        gl = canvas.getContext('webgl');
        if (gl == null) {
            message.innerHTML = "WebGL not supported";
            throw new Error("WebGL not supported");
        }
    }
    
    console.log("WebGL context created");
    
    let renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true, context: gl, preserveDrawingBuffer: true });
    let resolution = new THREE.Vector2(window.innerWidth, window.innerHeight);
    let time = 0.0;
    let clock = new THREE.Clock();

    console.log("Three.js renderer created");

    let scene = new THREE.Scene();
    let shaderMaterial = new THREE.ShaderMaterial({
        fragmentShader: document.getElementById('test-shader').textContent,
        depthWrite: false,
        depthTest: false,
        uniforms: {
            resolution: { type: 'v2', value: resolution },
            time: { type: 'f', value: 0.0 }
        }
    });
    
    let quad = new THREE.Mesh(
        new THREE.PlaneGeometry(2, 2),
        shaderMaterial
    );
    scene.add(quad);

    let camera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0.1, 10);
    camera.position.set(0, 0, 1);

    console.log("Scene setup complete");

    function computeSize() {
        resolution.set(window.innerWidth, window.innerHeight);
        renderer.setSize(resolution.x, resolution.y, false);
        shaderMaterial.uniforms.resolution.value = resolution;
    }

    function render() {
        requestAnimationFrame(render);
        
        time = clock.getElapsedTime();
        shaderMaterial.uniforms.time.value = time;
        
        renderer.render(scene, camera);
    }

    computeSize();
    message.innerHTML = "Raytracer loaded successfully!";
    setTimeout(() => { message.style.display = 'none'; }, 2000);
    render();

    window.addEventListener('resize', function() {
        computeSize();
    });
    
    console.log("Raytracer started");
</script>
</html>
