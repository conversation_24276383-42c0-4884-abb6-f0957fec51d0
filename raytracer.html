<!DOCTYPE html>
<html>
<head>
    <style>
        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            display: block;
        }
        #canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        .error {
            font-family: Consolas;
            font-size: 1.2em;
            color: black;
            box-sizing: border-box;
            background-color: lightcoral;
            border-radius: 2px;
            border-color: lightblue;
            border-width: thin;
            border-style: solid;
            line-height: 1.4em;
            cursor:pointer;
        }
        .error:hover {
            color: black;
            background-color: brown;
            border-color: blue;
        }
        #message {
            font-family: Consolas;
            font-size: 1.2em;
            color: #ccc;
            background-color: black;
            font-weight: bold;
            z-index: 2;
            position: absolute;
        }
    </style>
</head>
<body>
    <div id="message"></div>
    <canvas id="canvas"></canvas>
</body>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/110/three.min.js"></script>

<script id='raytracer-shader' type='x-shader/x-fragment'>
#define SOLUTION_CYLINDER_AND_PLANE
#define SOLUTION_SHADOW
#define SOLUTION_REFLECTION_REFRACTION
// #define SOLUTION_FRESNEL

// #define SOLUTION_BOOLEAN

precision highp float;
uniform ivec2 viewport;

struct PointLight
{
    vec3 position;
    vec3 color;
};

struct Material
{
    vec3 diffuse;
    vec3 specular;
    float glossiness;
    float reflection;
    float refraction;
    float ior;
};

struct Sphere
{
    vec3 position;
    float radius;
    Material material;
};

struct Plane
{
    vec3 normal;
    float d;
    Material material;
};

struct Cylinder
{
    vec3 position;
    vec3 direction;
    float radius;
    Material material;
};

const int BOOLEAN_MODE_AND = 0;   // and
const int BOOLEAN_MODE_MINUS = 1; // minus

struct Boolean
{
    Sphere spheres[2];
    int mode;
};

const int lightCount = 2;
const int sphereCount = 3;
const int planeCount = 1;
const int cylinderCount = 2;
const int booleanCount = 2;

struct Scene
{
    vec3 ambient;
    PointLight[lightCount] lights;
    Sphere[sphereCount] spheres;
    Plane[planeCount] planes;
    Cylinder[cylinderCount] cylinders;
    Boolean[booleanCount] booleans;
};

struct Ray
{
    vec3 origin;
    vec3 direction;
};

// Contains all information pertaining to a ray/object intersection
struct HitInfo
{
    bool hit;
    float t;
    vec3 position;
    vec3 normal;
    Material material;
    bool enteringPrimitive;
};

HitInfo getEmptyHit()
{
    return HitInfo(
        false,
        0.0,
        vec3(0.0),
        vec3(0.0),
        Material(vec3(0.0), vec3(0.0), 0.0, 0.0, 0.0, 0.0),
        false);
}

// Sorts the two t values such that t1 is smaller than t2
void sortT(inout float t1, inout float t2)
{
    // Make t1 the smaller t
    if (t2 < t1)
    {
        float temp = t1;
        t1 = t2;
        t2 = temp;
    }
}

// Tests if t is in an interval
bool isTInInterval(const float t, const float tMin, const float tMax)
{
    return t > tMin && t < tMax;
}

// Get the smallest t in an interval.
bool getSmallestTInInterval(float t0, float t1, const float tMin, const float tMax, inout float smallestTInInterval)
{

    sortT(t0, t1);

    // As t0 is smaller, test this first
    if (isTInInterval(t0, tMin, tMax))
    {
        smallestTInInterval = t0;
        return true;
    }

    // If t0 was not in the interval, still t1 could be
    if (isTInInterval(t1, tMin, tMax))
    {
        smallestTInInterval = t1;
        return true;
    }

    // None was
    return false;
}

HitInfo intersectSphere(const Ray ray, const Sphere sphere, const float tMin, const float tMax)
{

    vec3 to_sphere = ray.origin - sphere.position;

    float a = dot(ray.direction, ray.direction);
    float b = 2.0 * dot(ray.direction, to_sphere);
    float c = dot(to_sphere, to_sphere) - sphere.radius * sphere.radius;
    float D = b * b - 4.0 * a * c;
    if (D > 0.0)
    {
        float t0 = (-b - sqrt(D)) / (2.0 * a);
        float t1 = (-b + sqrt(D)) / (2.0 * a);

        float smallestTInInterval;
        if (!getSmallestTInInterval(t0, t1, tMin, tMax, smallestTInInterval))
        {
            return getEmptyHit();
        }

        vec3 hitPosition = ray.origin + smallestTInInterval * ray.direction;

        // Checking if we're inside the sphere by checking if the ray's origin is inside. If we are, then the normal
        // at the intersection surface points towards the center. Otherwise, if we are outside the sphere, then the normal
        // at the intersection surface points outwards from the sphere's center. This is important for refraction.
        vec3 normal =
            length(ray.origin - sphere.position) < sphere.radius + 0.001 ? -normalize(hitPosition - sphere.position) : normalize(hitPosition - sphere.position);

        // Checking if we're inside the sphere by checking if the ray's origin is inside,
        //  but this time for IOR bookkeeping.
        // If we are inside, set a flag to say we're leaving. If we are outside, set the flag to say we're entering.
        // This is also important for refraction.
        bool enteringPrimitive =
            length(ray.origin - sphere.position) < sphere.radius + 0.001 ? false : true;

        return HitInfo(
            true,
            smallestTInInterval,
            hitPosition,
            normal,
            sphere.material,
            enteringPrimitive);
    }
    return getEmptyHit();
}

HitInfo intersectPlane(const Ray ray, const Plane plane, const float tMin, const float tMax)
{
#ifdef SOLUTION_CYLINDER_AND_PLANE

    // Plane intersection using the equation:
    // plane.normal * p - plane.d = 0,
    // where p = ray.origin + t * ray.direction
    float a = dot(ray.direction, plane.normal);
    float b = dot(ray.origin, plane.normal) - plane.d; // -plane.d not + plane.d, otherwise we get the celling rather than the floor
    float t = -b / a;

    if (isTInInterval(t, tMin, tMax))
    {
        vec3 hitPosition = ray.origin + t * ray.direction;
        // checking if we're inside the plane by checking the angle between the ray direction and the plane normal
        // If the angle is positive, light is coming from under the plane, and we're inside. Otherwise, we're outside.
        vec3 normal = dot(ray.direction, plane.normal) < 0.0 ? plane.normal : -plane.normal;

        // If the angle between the ray direction and the normal is positive, we're leaving the plane. Otherwise, we're entering.
        bool enteringPrimitive = dot(ray.direction, normal) < 0.0;
        return HitInfo(
            true,
            t,
            hitPosition,
            normal,
            plane.material,
            enteringPrimitive);
    }
#endif
    return getEmptyHit();
}

float lengthSquared(vec3 x)
{
    return dot(x, x);
}

HitInfo intersectCylinder(const Ray ray, const Cylinder cylinder, const float tMin, const float tMax)
{
#ifdef SOLUTION_CYLINDER_AND_PLANE
    // Cylinder intersection using the equation:
    // (p - c - (v * dot(p - c, v))) . (p - c - (v * dot(p - c, v))) = r^2,
    // where p = ray.origin + t * ray.direction
    vec3 to_cylinder = ray.origin - cylinder.position;
    vec3 c = cylinder.position;
    vec3 v = cylinder.direction;
    vec3 p = ray.origin - c;
    vec3 d = ray.direction;

    float a = lengthSquared(d - dot(d, v) * v);
    float b = 2.0 * dot(p - dot(p, v) * v, d - dot(d, v) * v);
    float c_prime = lengthSquared(p - dot(p, v) * v) - cylinder.radius * cylinder.radius;
    float D = b * b - 4.0 * a * c_prime;

    if (D > 0.0)
    {
        float t0 = (-b - sqrt(D)) / (2.0 * a);
        float t1 = (-b + sqrt(D)) / (2.0 * a);

        float smallestTInInterval;
        if (!getSmallestTInInterval(t0, t1, tMin, tMax, smallestTInInterval))
        {
            return getEmptyHit();
        }

        vec3 hitPosition = ray.origin + smallestTInInterval * ray.direction;
        vec3 toHit = hitPosition - c;                    // vector from cylinder center to hit position
        vec3 projectedToHit = toHit - dot(toHit, v) * v; // vector from cylinder center to hit position, projected onto the cylinder direction, which is the cylinder's axis
        vec3 normal = normalize(projectedToHit);         // normal is the vector from the hit position to the cylinder's axis, normalized

        // If the angle between the ray direction and the normal is positive, we're leaving the cylinder. Otherwise, we're entering.
        bool enteringPrimitive = dot(ray.direction, normal) < 0.0;
        // If we're leaving the cylinder, the normal should point inwards. Otherwise, it should point outwards.
        normal = enteringPrimitive ? normal : -normal;

        return HitInfo(
            true,
            smallestTInInterval,
            hitPosition,
            normal,
            cylinder.material,
            enteringPrimitive);
    }
#endif
    return getEmptyHit();
}

bool inside(const vec3 position, const Sphere sphere)
{
    return length(position - sphere.position) < sphere.radius;
}

HitInfo intersectBoolean(const Ray ray, const Boolean boolean, const float tMin, const float tMax)
{
#ifdef SOLUTION_BOOLEAN
#else
    // Put your code for the boolean task in the #ifdef above!
#endif
    return getEmptyHit();
}

uniform float time;

HitInfo getBetterHitInfo(const HitInfo oldHitInfo, const HitInfo newHitInfo)
{
    if (newHitInfo.hit)
        if (newHitInfo.t < oldHitInfo.t) // No need to test for the interval, this has to be done per-primitive
            return newHitInfo;
    return oldHitInfo;
}

HitInfo intersectScene(const Scene scene, const Ray ray, const float tMin, const float tMax)
{
    HitInfo bestHitInfo;
    bestHitInfo.t = tMax;
    bestHitInfo.hit = false;

    for (int i = 0; i < booleanCount; ++i)
    {
        bestHitInfo = getBetterHitInfo(bestHitInfo, intersectBoolean(ray, scene.booleans[i], tMin, tMax));
    }

    for (int i = 0; i < planeCount; ++i)
    {
        bestHitInfo = getBetterHitInfo(bestHitInfo, intersectPlane(ray, scene.planes[i], tMin, tMax));
    }
    for (int i = 0; i < sphereCount; ++i)
    {
        bestHitInfo = getBetterHitInfo(bestHitInfo, intersectSphere(ray, scene.spheres[i], tMin, tMax));
    }
    for (int i = 0; i < cylinderCount; ++i)
    {
        bestHitInfo = getBetterHitInfo(bestHitInfo, intersectCylinder(ray, scene.cylinders[i], tMin, tMax));
    }

    return bestHitInfo;
}

vec3 shadeFromLight(
    const Scene scene,
    const Ray ray,
    const HitInfo hit_info,
    const PointLight light)
{
    vec3 hitToLight = light.position - hit_info.position;

    vec3 lightDirection = normalize(hitToLight);
    vec3 viewDirection = normalize(hit_info.position - ray.origin);
    vec3 reflectedDirection = reflect(viewDirection, hit_info.normal);
    float diffuse_term = max(0.0, dot(lightDirection, hit_info.normal));
    float specular_term = pow(max(0.0, dot(lightDirection, reflectedDirection)), hit_info.material.glossiness);

#ifdef SOLUTION_SHADOW
    // Define the shadow ray starting from the surface point, pointing to the light.
    Ray shadowRay = Ray(hit_info.position, lightDirection);

    // Calculate the distance from the surface to the light.
    float distToLight = length(hitToLight);

    // Intersect the shadow ray with the scene.
    HitInfo shadowHit = intersectScene(scene, shadowRay, 0.001, distToLight);

    // If the shadow ray hits something, the light is blocked. Otherwise, it's visible.
    float visibility = shadowHit.hit ? 0.0 : 1.0;
#else
    // Put your shadow test here
    float visibility = 1.0;
#endif
    return visibility *
           light.color * (specular_term * hit_info.material.specular + diffuse_term * hit_info.material.diffuse);
}

vec3 background(const Ray ray)
{
    // A simple implicit sky that can be used for the background
    return vec3(0.2) + vec3(0.8, 0.6, 0.5) * max(0.0, ray.direction.y);
}

// It seems to be a WebGL issue that the third parameter needs to be inout instead of const on Tobias' machine
vec3 shade(const Scene scene, const Ray ray, inout HitInfo hitInfo)
{

    if (!hitInfo.hit)
    {
        return background(ray);
    }

    vec3 shading = scene.ambient * hitInfo.material.diffuse;
    for (int i = 0; i < lightCount; ++i)
    {
        shading += shadeFromLight(scene, ray, hitInfo, scene.lights[i]);
    }
    return shading;
}

Ray getFragCoordRay(const vec2 frag_coord)
{
    float sensorDistance = 1.0;
    vec2 sensorMin = vec2(-1, -0.5);
    vec2 sensorMax = vec2(1, 0.5);
    vec2 pixelSize = (sensorMax - sensorMin) / vec2(viewport.x, viewport.y);
    vec3 origin = vec3(0, 0, sensorDistance);
    vec3 direction = normalize(vec3(sensorMin + pixelSize * frag_coord, -sensorDistance));

    return Ray(origin, direction);
}

float fresnel(const vec3 viewDirection, const vec3 normal, const float sourceIOR, const float destIOR)
{
#ifdef SOLUTION_FRESNEL
#else
    // Put your code to compute the Fresnel effect in the ifdef above
    return 1.0;
#endif
}

vec3 colorForFragment(const Scene scene, const vec2 fragCoord)
{

    Ray initialRay = getFragCoordRay(fragCoord);
    HitInfo initialHitInfo = intersectScene(scene, initialRay, 0.001, 10000.0);
    vec3 result = shade(scene, initialRay, initialHitInfo);

    Ray currentRay;
    HitInfo currentHitInfo;

    // Compute the reflection
    currentRay = initialRay;
    currentHitInfo = initialHitInfo;

    // The initial strength of the reflection
    float reflectionWeight = 1.0;

    // The initial medium is air
    float currentIOR = 1.0;

    float sourceIOR = 1.0;
    float destIOR = 1.0;

    const int maxReflectionStepCount = 2;
    for (int i = 0; i < maxReflectionStepCount; i++)
    {

        if (!currentHitInfo.hit)
            break;

#ifdef SOLUTION_REFLECTION_REFRACTION
        reflectionWeight *= currentHitInfo.material.reflection;
#else
        // Put your reflection weighting code in the ifdef above
#endif

#ifdef SOLUTION_FRESNEL
#else
        // Replace with Fresnel code in the ifdef above
        reflectionWeight *= 0.5;
#endif

        Ray nextRay;
#ifdef SOLUTION_REFLECTION_REFRACTION
        vec3 reflectionOrigin = currentHitInfo.position + 0.001 * currentHitInfo.normal;
        vec3 reflectionDirection = reflect(currentRay.direction, currentHitInfo.normal);
        nextRay = Ray(reflectionOrigin, reflectionDirection);
#else
        // Put your code to compute the reflection ray in the ifdef above
#endif
        currentRay = nextRay;

        currentHitInfo = intersectScene(scene, currentRay, 0.001, 10000.0);

        result += reflectionWeight * shade(scene, currentRay, currentHitInfo);
    }

    // Compute the refraction
    currentRay = initialRay;
    currentHitInfo = initialHitInfo;

    // The initial strength of the refraction.
    float refractionWeight = 1.0;

    const int maxRefractionStepCount = 2;
    for (int i = 0; i < maxRefractionStepCount; i++)
    {

#ifdef SOLUTION_REFLECTION_REFRACTION
        if (!currentHitInfo.hit)
            break;

        if (currentHitInfo.material.refraction == 0.0)
            break;

        refractionWeight *= currentHitInfo.material.refraction;
#else
        // Put your refraction weighting code in the ifdef above
        refractionWeight *= 0.5;
#endif

#ifdef SOLUTION_FRESNEL
#else
        // Put your Fresnel code in the ifdef above
#endif

        Ray nextRay;

#ifdef SOLUTION_REFLECTION_REFRACTION
        vec3 normal = currentHitInfo.normal;

        if (currentHitInfo.enteringPrimitive)
        {
            sourceIOR = 1.0;
            destIOR = currentHitInfo.material.ior;
        }
        else
        {
            sourceIOR = currentHitInfo.material.ior;
            destIOR = 1.0;
            normal = -normal;
        }

        vec3 refractionDirection = refract(currentRay.direction, normal, sourceIOR / destIOR);
        vec3 refractionOrigin;

        if (refractionDirection == vec3(0.0))
        {
            refractionDirection = reflect(currentRay.direction, normal);
            refractionOrigin = currentHitInfo.position + 0.001 * currentHitInfo.normal;
        }
        else
        {
            refractionOrigin = currentHitInfo.position - 0.001 * currentHitInfo.normal;
        }
        nextRay = Ray(refractionOrigin, refractionDirection);
        currentRay = nextRay;
#else
        float sourceIOR;
        float destIOR;
        // Put your code to compute the refraction ray and track the IOR in the ifdef above
#endif
        currentHitInfo = intersectScene(scene, currentRay, 0.001, 10000.0);

        result += refractionWeight * shade(scene, currentRay, currentHitInfo);

        if (!currentHitInfo.hit)
            break;
    }
    return result;
}

// Common materials
// IOR data comes from https://refractiveindex.info/, otherwise zero
// When reflection and refraction set to zero, it is turned off

Material getDefaultMaterial()
{
    return Material(vec3(0.3), vec3(0), 0.0, 0.0, 0.0, 0.0);
}

Material getPaperMaterial()
{
    return Material(vec3(0.7, 0.7, 0.7), vec3(0, 0, 0), 5.0, 0.0, 0.0, 0.0);
}

Material getPlasticMaterial()
{
    return Material(vec3(0.9, 0.3, 0.1), vec3(1.0), 10.0, 0.9, 0.0, 1.5);
}

Material getGlassMaterial()
{
    return Material(vec3(0.0), vec3(0.0), 5.0, 1.0, 1.0, 1.5);
}

Material getSteelMirrorMaterial()
{
    return Material(vec3(0.1), vec3(0.3), 20.0, 0.8, 0.0, 2.9);
}

Material getMetaMaterial()
{
    return Material(vec3(0.1, 0.2, 0.5), vec3(0.3, 0.7, 0.9), 20.0, 0.8, 0.0, 0.0);
}

vec3 tonemap(const vec3 radiance)
{
    const float monitorGamma = 2.0;
    return pow(radiance, vec3(1.0 / monitorGamma));
}

void main()
{
    // Setup scene
    Scene scene;
    scene.ambient = vec3(0.12, 0.15, 0.2);
    scene.lights[0].position = vec3(5, 15, -5);
    scene.lights[0].color = 0.5 * vec3(0.9, 0.5, 0.1);

    scene.lights[1].position = vec3(-15, 5, 2);
    scene.lights[1].color = 0.5 * vec3(0.1, 0.3, 1.0);

    // Primitives
    bool specialScene = false;

    // Set specialScene to true to implement the task in the below ifdef block
#ifdef SOLUTION_BOOLEAN
#endif

    if (specialScene)
    {
        // Boolean scene
        scene.booleans[0].mode = BOOLEAN_MODE_MINUS;

        // sphere A
        scene.booleans[0].spheres[0].position = vec3(3, 0, -10);
        scene.booleans[0].spheres[0].radius = 2.75;
        scene.booleans[0].spheres[0].material = getPaperMaterial();

        // sphere B
        scene.booleans[0].spheres[1].position = vec3(6, 1, -13);
        scene.booleans[0].spheres[1].radius = 4.0;
        scene.booleans[0].spheres[1].material = getPaperMaterial();

        scene.booleans[1].mode = BOOLEAN_MODE_AND;

        scene.booleans[1].spheres[0].position = vec3(-3.0, 1, -12);
        scene.booleans[1].spheres[0].radius = 4.0;
        scene.booleans[1].spheres[0].material = getPaperMaterial();

        scene.booleans[1].spheres[1].position = vec3(-6.0, 1, -12);
        scene.booleans[1].spheres[1].radius = 4.0;
        scene.booleans[1].spheres[1].material = getMetaMaterial();

        scene.planes[0].normal = normalize(vec3(0, 0.8, 0));
        scene.planes[0].d = -4.5;
        scene.planes[0].material = getSteelMirrorMaterial();

        scene.lights[0].position = vec3(-5, 25, -5);
        scene.lights[0].color = vec3(0.9, 0.5, 0.1);

        scene.lights[1].position = vec3(-15, 5, 2);
        scene.lights[1].color = 0.0 * 0.5 * vec3(0.1, 0.3, 1.0);
    }
    else
    {
        // normal scene
        scene.spheres[0].position = vec3(10, -5, -16);
        scene.spheres[0].radius = 6.0;
        scene.spheres[0].material = getPaperMaterial();

        scene.spheres[1].position = vec3(-7, -2, -13);
        scene.spheres[1].radius = 4.0;
        scene.spheres[1].material = getPlasticMaterial();

        scene.spheres[2].position = vec3(0, 0.5, -5);
        scene.spheres[2].radius = 2.0;
        scene.spheres[2].material = getGlassMaterial();

        scene.planes[0].normal = normalize(vec3(0, 1.0, 0));
        scene.planes[0].d = -4.5;
        scene.planes[0].material = getSteelMirrorMaterial();

        scene.cylinders[0].position = vec3(-1, 1, -26);
        scene.cylinders[0].direction = normalize(vec3(-2, 2, -1));
        scene.cylinders[0].radius = 1.5;
        scene.cylinders[0].material = getPaperMaterial();

        scene.cylinders[1].position = vec3(4, 1, -5);
        scene.cylinders[1].direction = normalize(vec3(1, 4, 1));
        scene.cylinders[1].radius = 0.4;
        scene.cylinders[1].material = getPlasticMaterial();
    }

    // Compute color for fragment
    gl_FragColor.rgb = tonemap(colorForFragment(scene, gl_FragCoord.xy));
    gl_FragColor.a = 1.0;
}
</script>

<script type="text/javascript">
    console.log("Starting raytracer...");

    let canvas = document.getElementById('canvas');
    let message = document.getElementById('message');

    let gl = canvas.getContext('webgl2');
    if (gl == null) {
        gl = canvas.getContext('webgl');
        if (gl == null) {
            message.innerHTML = "WebGL not supported";
            console.error("WebGL not supported");
        }
    }

    console.log("WebGL context created");

    let renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true, context: gl, preserveDrawingBuffer: true });
    let resolution = new THREE.Vector3(window.innerWidth, window.innerHeight, 1.0);
    let time = 0.0;
    let clock = new THREE.Clock();

    console.log("Three.js renderer created");

    let scene = new THREE.Scene();

    try {
        let shaderMaterial = new THREE.ShaderMaterial({
            fragmentShader: document.getElementById('raytracer-shader').textContent,
            depthWrite: false,
            depthTest: false,
            uniforms: {
                viewport: { type: 'v2', value: new THREE.Vector2(Math.floor(resolution.x), Math.floor(resolution.y)) },
                time: { type: 'f', value: 0.0 }
            }
        });

        let quad = new THREE.Mesh(
            new THREE.PlaneGeometry(resolution.x, resolution.y),
            shaderMaterial
        );
        scene.add(quad);

        console.log("Shader material created successfully");
    } catch (error) {
        console.error("Error creating shader material:", error);
        message.innerHTML = "Error creating shader: " + error.message;
    }

    let camera = new THREE.OrthographicCamera(-resolution.x / 2.0, resolution.x / 2.0, resolution.y / 2.0, -resolution.y / 2.0, 1, 1000);
    camera.position.set(0, 0, 10);

    let quad = scene.children[0]; // Get the quad from the scene

    function computeSize() {
        resolution = new THREE.Vector3(window.innerWidth, window.innerHeight, 1.0);
        renderer.setSize(resolution.x, resolution.y, false);

        // Update Camera and Mesh
        if (quad) {
            quad.geometry = new THREE.PlaneGeometry(resolution.x, resolution.y);
            quad.material.uniforms.viewport.value = new THREE.Vector2(Math.floor(resolution.x), Math.floor(resolution.y));
        }
        camera.left = -resolution.x / 2.0;
        camera.right = resolution.x / 2.0;
        camera.top = resolution.y / 2.0;
        camera.bottom = -resolution.y / 2.0;
        camera.updateProjectionMatrix();
    }

    function render() {
        requestAnimationFrame(render);

        time = clock.getElapsedTime();
        if (quad && quad.material.uniforms.time) {
            quad.material.uniforms.time.value = time;
        }

        renderer.render(scene, camera);
    }

    computeSize();
    console.log("Starting render loop");
    render();

    window.addEventListener('resize', function() {
        computeSize();
    });

    console.log("Raytracer initialization complete");
</script>
</html>
